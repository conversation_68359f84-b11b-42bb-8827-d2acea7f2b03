<template>
  <div class="mod-dashboard">
    <!-- Modern Apple-Inspired Header -->
    <header class="dashboard-header">
      <!-- Top Section: Title and Action Buttons -->
      <div class="header-top">
        <div class="header-title-section">
          <h1 class="header-title">Simon<PERSON></h1>
          <p class="header-subtitle">Advanced Sims 4 Mod Management & Intelligence</p>
        </div>

        <div class="header-actions">
          <button
            class="action-btn action-btn--primary"
            @click="emit('analyze-folder')"
            :disabled="isLoading"
          >
            <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {{ isLoading ? 'Analyzing...' : 'Analyze Mods Folder' }}
          </button>
          <button
            v-if="mods && mods.length > 0"
            class="action-btn action-btn--secondary"
            @click="extractThumbnails"
            :disabled="isExtractingThumbnails"
          >
            <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {{ isExtractingThumbnails ? 'Extracting...' : 'Extract Thumbnails' }}
          </button>
          <button
            v-if="mods && mods.length > 0"
            class="action-btn action-btn--secondary"
            @click="emit('export-results')"
          >
            <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>
          <button
            class="action-btn action-btn--secondary"
            @click="emit('open-settings')"
          >
            <svg class="action-btn-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
          </button>
          <div class="version-badge">v2.0.0</div>
        </div>
      </div>

      <!-- Middle Section: Search Bar -->
      <div class="header-middle">
        <div class="search-container">
          <div class="search-input-wrapper">
            <MagnifyingGlassIcon class="search-icon" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search your mod collection..."
              class="search-input"
              aria-label="Search mods"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="search-clear"
              aria-label="Clear search"
            >
              <XMarkIcon class="search-clear-icon" />
            </button>
          </div>
        </div>
      </div>

      <!-- Bottom Section: Filters and Controls -->
      <div class="header-bottom">
        <div class="filters-container">
          <div class="filter-group">
            <label class="filter-label">Type:</label>
            <select v-model="selectedFileTypeFilter" class="filter-select" aria-label="Filter by file type">
              <option value="">All Types</option>
              <option value=".package">Package Files</option>
              <option value=".ts4script">Script Files</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Quality:</label>
            <select v-model="selectedQualityFilter" class="filter-select" aria-label="Filter by quality">
              <option value="">All Quality</option>
              <option value="excellent">Excellent (90-100)</option>
              <option value="good">Good (70-89)</option>
              <option value="fair">Fair (50-69)</option>
              <option value="poor">Poor (0-49)</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Sort:</label>
            <select v-model="selectedSortOption" class="filter-select" aria-label="Sort options">
              <option value="name">Sort by Name</option>
              <option value="quality">Sort by Quality</option>
              <option value="size">Sort by Size</option>
              <option value="author">Sort by Author</option>
            </select>
          </div>
        </div>

        <div class="header-controls">
          <div class="results-info">
            <span class="results-count">{{ filteredMods?.length || 0 }}</span>
            <span class="results-text">mods</span>
            <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
          </div>

          <div class="view-controls">
            <div class="thumbnail-size-controls">
              <button
                @click="thumbnailSize = 'small'"
                :class="{ active: thumbnailSize === 'small' }"
                class="size-btn"
                title="Small thumbnails"
                aria-label="Small thumbnail size"
              >
                <Squares2X2Icon />
              </button>
              <button
                @click="thumbnailSize = 'medium'"
                :class="{ active: thumbnailSize === 'medium' }"
                class="size-btn"
                title="Medium thumbnails"
                aria-label="Medium thumbnail size"
              >
                <Square3Stack3DIcon />
              </button>
              <button
                @click="thumbnailSize = 'large'"
                :class="{ active: thumbnailSize === 'large' }"
                class="size-btn"
                title="Large thumbnails"
                aria-label="Large thumbnail size"
              >
                <RectangleStackIcon />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
    






    <!-- Main Content Area -->
    <main class="dashboard-main">
      <!-- Phase 1: Folder Scanning (Spinner Only) -->
      <div
        v-if="isScanningFolder"
        class="folder-scanning-state"
        role="status"
        aria-live="polite"
        aria-label="Scanning mods folder"
      >
        <div class="loading-header">
          <div class="loading-spinner" aria-hidden="true"></div>
          <p class="loading-text">Scanning mods folder...</p>
          <p class="loading-subtext">Discovering mod files and analyzing structure</p>
        </div>
      </div>

      <!-- Phase 2: Mod Processing (Skeleton Grid) -->
      <div
        v-else-if="isProcessingMods && mods && mods.length > 0"
        class="mod-processing-state"
        role="status"
        aria-live="polite"
        :aria-label="`Processing ${mods.length} mods`"
      >
        <!-- Progress indicator -->
        <div class="loading-header">
          <div class="loading-spinner" aria-hidden="true"></div>
          <p class="loading-text">Analyzing {{ mods.length }} mods with advanced intelligence...</p>
          <p class="loading-subtext">Processing mod content, extracting metadata, and generating thumbnails</p>
        </div>

        <!-- Skeleton Grid with Actual Mod Count -->
        <div class="skeleton-container">
          <SkeletonGrid
            :count="Math.min(mods.length, 24)"
            :size="thumbnailSize"
            animation="shimmer"
            :staggered="true"
          />
        </div>
      </div>

      <!-- Thumbnail Grid (Primary View) -->
      <div v-else-if="filteredMods.length > 0" class="thumbnail-gallery">
        <div
          class="thumbnail-grid"
          :class="[
            `thumbnail-grid--${thumbnailSize}`,
            'thumbnail-grid--loaded'
          ]"
          role="grid"
          aria-label="Mod collection"
        >
          <article
            v-for="(mod, index) in paginatedMods"
            :key="mod.fileName || index"
            class="thumbnail-item"
            :class="'thumbnail-item--loaded'"
            :style="{ animationDelay: `${index * 50}ms` }"
            role="gridcell"
            :tabindex="0"
            @click="openModDetails(mod)"
            @keydown.enter="openModDetails(mod)"
            @keydown.space.prevent="openModDetails(mod)"
            :aria-label="`View details for ${getModDisplayName(mod)} by ${mod?.author || 'Unknown Author'}. Quality score: ${getQualityScore(mod) || 'Unknown'}`"
            :aria-describedby="`mod-${mod.id || index}-description`"
          >
            <div class="thumbnail-container">
              <!-- Thumbnail Image -->
              <div class="thumbnail-image">
                <img
                  v-if="mod?.thumbnailUrl"
                  :src="mod.thumbnailUrl"
                  :alt="`${getModDisplayName(mod)} thumbnail`"
                  class="thumbnail-img"
                  loading="lazy"
                  @error="handleThumbnailError(mod)"
                />
                <div v-else class="thumbnail-fallback">
                  <component :is="getCategoryIcon(mod)" class="fallback-icon" />
                  <!-- Debug info -->
                  <div class="debug-info" style="position: absolute; bottom: 0; left: 0; background: rgba(0,0,0,0.8); color: white; font-size: 10px; padding: 2px;">
                    {{ mod.thumbnailUrl ? 'HAS THUMB' : 'NO THUMB' }}
                  </div>
                </div>
              </div>

              <!-- Thumbnail Overlay -->
              <div class="thumbnail-overlay">
                <h3 class="thumbnail-title">{{ getModDisplayName(mod) }}</h3>
                <p v-if="mod?.author" class="thumbnail-author">by {{ mod.author }}</p>
                <!-- Screen reader description -->
                <div
                  :id="`mod-${mod.id || index}-description`"
                  class="sr-only"
                  aria-hidden="true"
                >
                  Mod file: {{ mod.fileName }}.
                  File size: {{ formatFileSize(mod.fileSize) }}.
                  Quality score: {{ getQualityScore(mod) || 'Unknown' }}%.
                  {{ mod.packageType ? `Package type: ${mod.packageType}.` : '' }}
                  {{ mod.resourceCount ? `Contains ${mod.resourceCount} resources.` : '' }}
                </div>
              </div>

              <!-- Quality Badge -->
              <div v-if="getQualityScore(mod)" class="quality-badge" :class="getQualityClass(mod)">
                {{ getQualityScore(mod) }}
              </div>

              <!-- File Type Badge -->
              <div class="file-type-badge" :class="getFileTypeClass(mod)">
                {{ getFileTypeLabel(mod) }}
              </div>
            </div>
          </article>
        </div>

        <!-- Pagination -->
        <nav v-if="totalPages > 1" class="pagination" aria-label="Pagination navigation">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="pagination-btn"
            aria-label="Previous page"
          >
            Previous
          </button>
          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
            aria-label="Next page"
          >
            Next
          </button>
        </nav>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-state">
        <div class="empty-state__icon">
          <FolderOpenIcon />
        </div>
        <h2 class="empty-state__title">No mods found</h2>
        <p class="empty-state__description">
          {{ hasActiveFilters ?
            'Try adjusting your filters to see more results.' :
            'Start by analyzing your Sims 4 mods folder to explore your collection.' }}
        </p>
        <button v-if="hasActiveFilters" @click="clearAllFilters" class="empty-state__action">
          Clear All Filters
        </button>
      </div>
    </main>

    <!-- Mod Details Modal -->
    <div
      v-if="selectedMod"
      class="mod-details-modal"
      @click.self="closeModDetails"
      @keydown.esc="closeModDetails"
      role="dialog"
      aria-modal="true"
      :aria-labelledby="`modal-title-${selectedMod.id}`"
      :aria-describedby="`modal-description-${selectedMod.id}`"
    >
      <div class="mod-details-content" tabindex="-1">
        <header class="mod-details-header">
          <div class="mod-details-title-section">
            <h2
              :id="`modal-title-${selectedMod.id}`"
              class="mod-details-title"
            >
              {{ getModDisplayName(selectedMod) }}
            </h2>
            <p
              v-if="selectedMod?.author"
              :id="`modal-description-${selectedMod.id}`"
              class="mod-details-author"
            >
              by {{ selectedMod.author }}
            </p>
          </div>
          <button @click="closeModDetails" class="mod-details-close" aria-label="Close details">
            <XMarkIcon />
          </button>
        </header>

        <div class="mod-details-body">
          <!-- Mod Image/Thumbnail -->
          <div class="mod-details-image">
            <img
              v-if="selectedMod?.thumbnailUrl"
              :src="selectedMod.thumbnailUrl"
              :alt="`${getModDisplayName(selectedMod)} preview`"
              class="mod-preview-img"
            />
            <div v-else class="mod-preview-fallback">
              <component :is="getCategoryIcon(selectedMod)" class="preview-fallback-icon" />
            </div>
          </div>

          <!-- Mod Information -->
          <div class="mod-details-info">
            <div class="mod-info-grid">
              <div class="mod-info-item">
                <span class="mod-info-label">File Name</span>
                <span class="mod-info-value">{{ selectedMod?.fileName || 'Unknown' }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Size</span>
                <span class="mod-info-value">{{ formatFileSize(selectedMod?.fileSize || 0) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Type</span>
                <span class="mod-info-value">{{ getFileTypeLabel(selectedMod) }}</span>
              </div>
              <div v-if="selectedMod?.version" class="mod-info-item">
                <span class="mod-info-label">Version</span>
                <span class="mod-info-value">{{ selectedMod?.version || 'Unknown' }}</span>
              </div>
              <div v-if="getQualityScore(selectedMod)" class="mod-info-item">
                <span class="mod-info-label">Quality Score</span>
                <span class="mod-info-value">{{ getQualityScore(selectedMod) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">Category</span>
                <span class="mod-info-value">{{ getModCategory(selectedMod) }}</span>
              </div>
            </div>

            <!-- Additional Details -->
            <div v-if="selectedMod?.resourceCount" class="mod-additional-info">
              <h3>Technical Details</h3>
              <p>Resources: {{ selectedMod?.resourceCount || 0 }}</p>
              <p v-if="selectedMod?.processingTime">Processing Time: {{ selectedMod.processingTime }}ms</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, shallowRef, shallowReactive, nextTick } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  Square3Stack3DIcon,
  RectangleStackIcon,
  FolderOpenIcon,
  // Category icons
  UserIcon,
  HomeIcon,
  CogIcon,
  CommandLineIcon,
  CubeIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';
import SkeletonGrid from './SkeletonGrid.vue';

// Props
const props = defineProps<{
  mods?: any[];
  /** @deprecated Use isScanningFolder and isProcessingMods instead */
  isLoading?: boolean;
  /** Phase 1: Scanning folder for mod files */
  isScanningFolder?: boolean;
  /** Phase 2: Processing discovered mods (when skeleton should show) */
  isProcessingMods?: boolean;
}>();

// Emits
const emit = defineEmits<{
  'analyze-folder': [];
  'export-results': [];
  'open-settings': [];
}>();

// Create a local reactive copy of mods that we can modify
const localMods = ref<any[]>([]);

// Use computed for reactive props - ensure reactivity
const mods = computed(() => {
  return localMods.value.length > 0 ? localMods.value : (props.mods || []);
});

// Watch for prop changes and update local copy
watch(() => props.mods, (newMods) => {
  if (newMods && newMods.length > 0 && localMods.value.length === 0) {
    // Only update if we don't have local mods yet (to preserve thumbnails)
    localMods.value = [...newMods];
  }
}, { immediate: true });
// Computed loading states for different phases
const isScanningFolder = computed(() => props.isScanningFolder || false);
const isProcessingMods = computed(() => props.isProcessingMods || false);
const isLoading = computed(() => props.isLoading || isScanningFolder.value || isProcessingMods.value);

// Watch for loading state changes
watch([() => props.isScanningFolder, () => props.isProcessingMods, () => props.isLoading], ([newScanning, newProcessing, newLoading], [oldScanning, oldProcessing, oldLoading]) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Reactive state - using shallow reactivity for performance
const searchQuery = ref('');
const selectedFileTypeFilter = ref('');
const selectedQualityFilter = ref('');
const selectedSortOption = ref('name');
const currentPage = ref(1);
const itemsPerPage = ref(24); // Better for grid layouts

// Thumbnail-first specific state
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium');
const selectedMod = ref<any>(null);

// Use shallowReactive for collections that change frequently
const modThumbnails = shallowRef<any[]>([]);
const selectedMods = shallowReactive(new Set<string>());

// Thumbnail extraction state
const isExtractingThumbnails = ref(false);
const thumbnailProgress = ref(0);

// Image preview state
const showImagePreview = ref(false);
const previewThumbnailIndex = ref(0);

// Computed properties
const totalMods = computed(() => {
  return mods.value?.length || 0;
});



// Use shallowRef for filtered results to optimize performance
const filteredMods = shallowRef<any[]>([]);

// Watch for changes and update filtered mods
watch(
  [mods, searchQuery, selectedFileTypeFilter, selectedQualityFilter, selectedSortOption],
  () => {
    if (!mods.value || !Array.isArray(mods.value)) {
      filteredMods.value = [];
      return;
    }

    let filtered = [...mods.value];

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(mod =>
        (mod.fileName && mod.fileName.toLowerCase().includes(query)) ||
        (mod.author && mod.author.toLowerCase().includes(query)) ||
        (mod.modName && mod.modName.toLowerCase().includes(query))
      );
    }

    // File type filter
    if (selectedFileTypeFilter.value) {
      filtered = filtered.filter(mod =>
        mod && mod.fileExtension === selectedFileTypeFilter.value
      );
    }

  // Quality filter
  if (selectedQualityFilter.value) {
    filtered = filtered.filter(mod => {
      if (!mod || typeof mod.qualityScore !== 'number') return false;
      const score = mod.qualityScore;
      switch (selectedQualityFilter.value) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'fair': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        case 'high': return score >= 80;
        case 'medium': return score >= 60 && score < 80;
        case 'low': return score < 60;
        default: return true;
      }
    });
  }

    // Sort
    filtered.sort((a, b) => {
      switch (selectedSortOption.value) {
        case 'name':
          return (a.fileName || '').localeCompare(b.fileName || '');
        case 'quality':
          return (b.qualityScore || 0) - (a.qualityScore || 0);
        case 'size':
          return (b.fileSize || 0) - (a.fileSize || 0);
        case 'author':
          return (a.author || '').localeCompare(b.author || '');
        case 'intelligence':
          return (a.intelligenceType || '').localeCompare(b.intelligenceType || '');
        default:
          return 0;
      }
    });

    filteredMods.value = filtered;
  },
  { immediate: true }
);

const totalPages = computed(() =>
  Math.ceil((filteredMods.value?.length || 0) / itemsPerPage.value)
);

// Use shallowRef for paginated results
const paginatedMods = shallowRef<any[]>([]);

// Watch for pagination changes
watch(
  [filteredMods, currentPage, itemsPerPage],
  () => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    paginatedMods.value = filteredMods.value?.slice(start, end) || [];
  },
  { immediate: true }
);

const hasActiveFilters = computed(() =>
  searchQuery.value ||
  selectedFileTypeFilter.value ||
  selectedQualityFilter.value
);



// Watch for filtered mods changes (moved here after computed properties are defined)
watch(filteredMods, (newFiltered, oldFiltered) => {
  // Filtered mods changed - trigger reactivity
}, { immediate: true });

// Watch for mods changes to trigger thumbnail extraction
watch(mods, (newMods) => {
  console.log('👀 [ModDashboard] Mods changed:', newMods?.length, 'mods, isExtracting:', isExtractingThumbnails.value);

  // Write debug info to help diagnose the issue
  if (newMods && newMods.length > 0) {
    console.log('📊 [ModDashboard] First mod sample:', {
      fileName: newMods[0]?.fileName,
      hasFilePath: !!newMods[0]?.filePath,
      filePath: newMods[0]?.filePath,
      hasThumbnailUrl: !!newMods[0]?.thumbnailUrl,
      thumbnailUrlLength: newMods[0]?.thumbnailUrl?.length || 0
    });
  }

  // Only trigger extraction if we have new mods without thumbnails
  if (newMods && newMods.length > 0 && !isExtractingThumbnails.value) {
    const hasAnyThumbnails = newMods.some(mod => mod?.thumbnailUrl);
    if (!hasAnyThumbnails) {
      console.log('🚀 [ModDashboard] Triggering automatic thumbnail extraction');
      extractThumbnails();
    } else {
      console.log('✅ [ModDashboard] Mods already have thumbnails, skipping extraction');
    }
  }
}, { immediate: true });

// Methods
const getModDisplayName = (mod: any): string => {
  // Priority 1: Use actual mod name from StringTable analysis if available
  if (mod.actualModName && mod.actualModName !== 'Unknown Mod') {
    return mod.actualModName;
  }

  // Priority 2: Use metadata mod name if available
  if (mod.modName && mod.modName !== 'Unknown Mod') {
    return mod.modName;
  }

  // Priority 3: Clean up the filename
  if (mod.fileName) {
    let name = mod.fileName
      .replace(/\.(package|ts4script)$/i, '') // Remove file extension
      .replace(/\[.*?\]/g, '') // Remove brackets and content (like [Author])
      .replace(/\{.*?\}/g, '') // Remove curly braces and content
      .replace(/\(.*?\)/g, '') // Remove parentheses and content
      .replace(/_+/g, ' ') // Replace underscores with spaces
      .replace(/-+/g, ' ') // Replace dashes with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    // Clean up common prefixes/suffixes
    name = name
      .replace(/^(mod|package|script|cc|custom|content)\s+/i, '') // Remove common prefixes
      .replace(/\s+(mod|package|script|cc|custom|content)$/i, '') // Remove common suffixes
      .replace(/^(the|a|an)\s+/i, '') // Remove articles at start
      .trim();

    // If we still have a meaningful name, format it nicely
    if (name && name.length > 0 && name !== 'Unknown') {
      // Handle special cases like "newemotionaltraits - arroganttrait"
      if (name.includes(' - ')) {
        return name.split(' - ')
          .map(part => part.split(' ')
            .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
            .filter(word => word.length > 0)
            .join(' '))
          .filter(part => part.length > 0)
          .join(' - ');
      } else {
        // Capitalize first letter of each word
        return name.split(' ')
          .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
          .filter(word => word.length > 0)
          .join(' ');
      }
    }
  }

  return 'Unknown Mod';
};

const getQualityScore = (mod: any): string => {
  if (!mod) return '';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore;
  if (typeof score === 'number') {
    // Quality scores are already in 0-100 range from QualityAssessmentAnalyzer
    const percentage = Math.round(Math.max(0, Math.min(100, score)));
    return `${percentage}%`;
  }
  return '';
};

const getQualityClass = (mod: any): string => {
  if (!mod) return 'quality-poor';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore || 0;
  if (score >= 0.8) return 'quality-excellent';
  if (score >= 0.6) return 'quality-good';
  if (score >= 0.4) return 'quality-fair';
  return 'quality-poor';
};

const getCategoryIcon = (mod: any) => {
  // Return appropriate icon component based on mod category
  // For now, return a default icon - you can expand this based on categories
  return 'CubeIcon'; // Default icon
};

const getFileTypeClass = (mod: any): string => {
  if (!mod) return 'file-type-package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'file-type-script' : 'file-type-package';
};

const getFileTypeLabel = (mod: any): string => {
  if (!mod) return 'Package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'Script' : 'Package';
};

const getModCategory = (mod: any): string => {
  if (!mod) return 'Unknown';
  // Try to get category from various sources
  if (mod.universalClassification?.category) return mod.universalClassification.category;
  if (mod.objectClassification?.category) return mod.objectClassification.category;
  if (mod.category) return mod.category;
  return 'Unknown';
};

const clearSearch = () => {
  searchQuery.value = '';
};

const clearAllFilters = () => {
  searchQuery.value = '';
  selectedFileTypeFilter.value = '';
  selectedQualityFilter.value = '';
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  selectedSortOption.value = field;
  currentPage.value = 1;
};

const formatBytes = (bytes: number | null | undefined): string => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatFileSize = (bytes: number | null | undefined): string => {
  return formatBytes(bytes);
};

// Modal and interaction methods
const openModDetails = (mod: any) => {
  if (mod) {
    selectedMod.value = mod;
  }
};

const closeModDetails = () => {
  selectedMod.value = null;
};

const handleThumbnailError = (mod: any) => {
  // Could set a fallback thumbnail URL here
};

// Watch for filter changes to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Thumbnail methods
const openImagePreview = (thumbnail: any) => {
  const index = modThumbnails.value.findIndex(t => t.id === thumbnail.id);
  if (index !== -1) {
    previewThumbnailIndex.value = index;
    showImagePreview.value = true;
  }
};

const closeImagePreview = () => {
  showImagePreview.value = false;
};



const showModDetails = (thumbnail: any) => {
  if (!thumbnail || !thumbnail.modFileName) return;

  // Find the corresponding mod and show its details
  const mod = mods.value?.find(m => m && m.fileName === thumbnail.modFileName);
  if (mod) {
    // Could emit an event or show a modal with mod details
  }
};

const onThumbnailLoad = (thumbnail: any) => {
  // Handle successful thumbnail load
};

const onThumbnailError = (thumbnail: any) => {
  // Handle thumbnail load error
};

const extractThumbnails = async () => {
  console.log('🎨 [ModDashboard] Starting thumbnail extraction for', mods.value?.length, 'mods');
  if (!mods.value || mods.value.length === 0) {
    console.log('❌ [ModDashboard] No mods to extract thumbnails for');
    return;
  }

  isExtractingThumbnails.value = true;
  thumbnailProgress.value = 0;
  modThumbnails.value = [];

  try {
    // Import the thumbnail extraction service
    const { ThumbnailExtractionService } = await import('../../services/visual/ThumbnailExtractionService');

    const totalMods = mods.value.length;
    let processedMods = 0;

    // Create a new array to trigger reactivity
    const updatedMods = [...mods.value];

    for (let i = 0; i < updatedMods.length; i++) {
      const mod = updatedMods[i];
      try {
        console.log(`🔍 [ModDashboard] Processing mod: ${mod.fileName}, has filePath: ${!!mod.filePath}`);
        // Try to extract actual thumbnails if we have file path
        if (mod.filePath) {
          try {
            const fs = await import('fs');
            const buffer = fs.readFileSync(mod.filePath);

            const result = await ThumbnailExtractionService.extractThumbnails(
              buffer,
              mod.fileName,
              {
                maxThumbnails: 1,
                preferredFormat: 'webp',
                maxWidth: 256,
                maxHeight: 256,
                prioritizeCasThumbnails: true,
                generateFallbacks: true
              }
            );

            if (result.success && result.thumbnails.length > 0) {
              const thumbnail = result.thumbnails[0];
              console.log(`✅ [ModDashboard] Successfully extracted thumbnail for ${mod.fileName}, data length:`, thumbnail.imageData.length);

              // Create a new mod object with thumbnail data to trigger reactivity
              updatedMods[i] = {
                ...mod,
                thumbnailUrl: thumbnail.imageData,
                thumbnailData: thumbnail.imageData
              };

              modThumbnails.value.push(thumbnail);
            } else {
              console.log(`⚠️ [ModDashboard] No thumbnails extracted for ${mod.fileName}, using fallback`);
              // Create fallback thumbnail
              const fallbackThumbnail = createFallbackThumbnail(mod);
              updatedMods[i] = {
                ...mod,
                thumbnailUrl: fallbackThumbnail.imageData,
                thumbnailData: fallbackThumbnail.imageData
              };
              modThumbnails.value.push(fallbackThumbnail);
            }
          } catch (fileError) {
            console.warn(`Failed to extract thumbnail for ${mod.fileName}:`, fileError);
            // Create fallback thumbnail
            const fallbackThumbnail = createFallbackThumbnail(mod);
            updatedMods[i] = {
              ...mod,
              thumbnailUrl: fallbackThumbnail.imageData,
              thumbnailData: fallbackThumbnail.imageData
            };
            modThumbnails.value.push(fallbackThumbnail);
          }
        } else {
          // Create fallback thumbnail for mods without file path
          const fallbackThumbnail = createFallbackThumbnail(mod);
          updatedMods[i] = {
            ...mod,
            thumbnailUrl: fallbackThumbnail.imageData,
            thumbnailData: fallbackThumbnail.imageData
          };
          modThumbnails.value.push(fallbackThumbnail);
        }

      } catch (error) {
        console.warn(`Failed to process thumbnail for ${mod.fileName}:`, error);
        // Create fallback thumbnail as last resort
        const fallbackThumbnail = createFallbackThumbnail(mod);
        updatedMods[i] = {
          ...mod,
          thumbnailUrl: fallbackThumbnail.imageData,
          thumbnailData: fallbackThumbnail.imageData
        };
        modThumbnails.value.push(fallbackThumbnail);
      }

      processedMods++;
      thumbnailProgress.value = (processedMods / totalMods) * 100;
    }

    // Update the local mods array to trigger reactivity
    localMods.value = updatedMods;

    // Force a re-render
    await nextTick();
    console.log('🎨 [ModDashboard] Thumbnail extraction completed, UI should update');

  } catch (error) {
    console.error('❌ [ModDashboard] Error extracting thumbnails:', error);
  } finally {
    isExtractingThumbnails.value = false;
  }
};

const createFallbackThumbnail = (mod: any): any => {
  const category = mod.category || 'unknown';
  const imageData = createMockThumbnailData(mod);

  return {
    id: `thumb_${mod.fileName || 'unknown'}_${Date.now()}`,
    modFileName: mod.fileName || 'unknown',
    resourceType: 'fallback',
    resourceKey: 'fallback_key',
    imageData,
    format: 'svg' as const,
    width: 256,
    height: 256,
    category,
    subcategory: mod.subcategory || null,
    confidence: 0.4,
    extractionMethod: 'category_fallback' as const,
    fileSize: mod.fileSize || 0,
    isHighQuality: false,
    isFallback: true
  };
};

const createMockThumbnailData = (mod: any): string => {
  const category = mod.category || 'unknown';

  // Enhanced category-based icon system
  const getCategoryConfig = (cat: string) => {
    const lowerCat = cat.toLowerCase();

    if (lowerCat.includes('cas') || lowerCat.includes('clothing') || lowerCat.includes('hair')) {
      return {
        primaryColor: '#EC4899',
        secondaryColor: '#BE185D',
        icon: '👕',
        label: 'CAS Content'
      };
    }

    if (lowerCat.includes('object') || lowerCat.includes('furniture') || lowerCat.includes('build')) {
      return {
        primaryColor: '#059669',
        secondaryColor: '#047857',
        icon: '🪑',
        label: 'Objects'
      };
    }

    if (lowerCat.includes('script') || lowerCat.includes('mod')) {
      return {
        primaryColor: '#7C3AED',
        secondaryColor: '#5B21B6',
        icon: '⚙️',
        label: 'Script Mod'
      };
    }

    if (lowerCat.includes('tuning')) {
      return {
        primaryColor: '#DC2626',
        secondaryColor: '#B91C1C',
        icon: '🔧',
        label: 'Tuning'
      };
    }

    return {
      primaryColor: '#6B7280',
      secondaryColor: '#4B5563',
      icon: '📦',
      label: 'Unknown'
    };
  };

  const config = getCategoryConfig(category);
  const fileName = (mod.fileName || 'Unknown').replace(/\.(package|ts4script)$/i, '');
  const truncatedName = fileName.length > 20 ? fileName.substring(0, 17) + '...' : fileName;

  const svg = `<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="categoryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${config.primaryColor};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${config.secondaryColor};stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="256" height="256" fill="url(#categoryGrad)" rx="12"/>
    <text x="128" y="100" text-anchor="middle" font-size="48">${config.icon}</text>
    <text x="128" y="140" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="600">${config.label}</text>
    <text x="128" y="160" text-anchor="middle" fill="white" font-family="system-ui, -apple-system, sans-serif" font-size="11" opacity="0.9">${truncatedName}</text>
  </svg>`;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Note: Thumbnail extraction is handled automatically when mods are loaded

// Lifecycle
onMounted(() => {
  // Any initialization logic
});
</script>

<style scoped>
/* ===== MODERN OKLCH COLOR INTEGRATION ===== */
/* Using the comprehensive color system from simonitor-design-system.css */

.mod-dashboard {
  background: var(--bg-secondary);
  min-height: 100vh;
  font-family: var(--font-family-sans);
}

/* ===== MODERN APPLE-INSPIRED HEADER ===== */
.dashboard-header {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

/* Header Top Section - Title and Actions */
.header-top {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-8) var(--space-4) var(--space-8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-6);
}

.header-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.header-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--plumbob-blue-500), var(--sage-green-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: var(--tracking-tight);
}

.header-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: var(--font-medium);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  border: none;
  white-space: nowrap;
}

.action-btn--primary {
  background: var(--plumbob-blue-500);
  color: white;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.action-btn--primary:hover:not(:disabled) {
  background: var(--plumbob-blue-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.action-btn--primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn--secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.action-btn--secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.action-btn-icon {
  width: 16px;
  height: 16px;
}

.version-badge {
  padding: var(--space-1) var(--space-3);
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  font-family: var(--font-family-mono);
}

/* Header Middle Section - Search */
.header-middle {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-8) var(--space-4) var(--space-8);
}

.search-container {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 56px;
  padding: 0 var(--space-12) 0 var(--space-12);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  font-family: var(--font-family-sans);
  font-size: var(--text-lg);
  font-weight: var(--font-normal);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.search-input::placeholder {
  color: var(--text-muted);
  font-weight: var(--font-normal);
}

.search-input:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
}

.search-input:focus {
  outline: none;
  border-color: var(--plumbob-blue-500);
  box-shadow: 0 0 0 4px oklch(from var(--plumbob-blue-500) l c h / 0.1), var(--shadow-md);
}

.search-clear {
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  padding: var(--space-1);
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--duration-150) var(--ease-out);
}

.search-clear:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.search-clear-icon {
  width: 16px;
  height: 16px;
}

/* Header Bottom Section - Filters and Controls */
.header-bottom {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-8) var(--space-5) var(--space-8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-8);
  border-top: 1px solid var(--border-light);
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(8px);
  margin-bottom: 0;
}

.filters-container {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  flex-wrap: wrap;
  flex: 1;
  justify-content: flex-start;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-width: 0;
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  white-space: nowrap;
  min-width: fit-content;
}

.filter-select {
  min-width: 140px;
  height: 40px;
  padding: 0 var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-family: var(--font-family-sans);
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='m19.5 8.25-7.5 7.5-7.5-7.5' /%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  background-size: 16px 16px;
  padding-right: var(--space-10);
  box-shadow: var(--shadow-sm);
}

.filter-select:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-md);
}

.filter-select:focus {
  outline: none;
  border-color: var(--plumbob-blue-500);
  box-shadow: 0 0 0 3px oklch(from var(--plumbob-blue-500) l c h / 0.1);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  flex-shrink: 0;
}

.results-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.results-count {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
  color: var(--plumbob-blue-500);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-tight);
}

.results-text {
  font-family: var(--font-family-sans);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.results-filtered {
  font-family: var(--font-family-sans);
  font-weight: var(--font-medium);
  color: var(--sunset-orange-500);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.view-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.thumbnail-size-controls {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
  box-shadow: var(--shadow-sm);
}

.size-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.size-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.size-btn.active {
  background: var(--plumbob-blue-500);
  color: white;
  box-shadow: 0 1px 3px rgba(37, 99, 235, 0.3);
}

.size-btn svg {
  width: 18px;
  height: 18px;
}

/* ===== RESULTS SUMMARY ===== */
.results-summary {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-8) var(--space-4) var(--space-8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.results-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.results-count strong {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

/* ===== MOD GRID ===== */
.mods-grid {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-8) var(--space-8) var(--space-8);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
  align-items: start;
}

/* ===== LOADING STATES ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  gap: var(--space-4);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--coral-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* ===== EMPTY STATES ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  gap: var(--space-6);
  text-align: center;
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  color: var(--text-muted);
}

.empty-state-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.empty-state-description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
  line-height: 1.6;
  margin: 0;
}

/* Results */
.results-summary {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-summary__info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.results-count {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-tight);
}

.results-text {
  font-family: var(--font-family-system);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.results-filtered {
  font-family: var(--font-family-system);
  font-weight: var(--font-medium);
  color: var(--primary-coral);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.view-options {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.view-option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.view-option.active {
  background: var(--primary-teal);
  color: var(--text-inverse);
  box-shadow: 0 1px 3px rgba(23, 163, 152, 0.3);
}

.view-option svg {
  width: 16px;
  height: 16px;
}

/* Content */
.dashboard-main {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-6) var(--space-6) var(--space-6);
}

.mod-results {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-16) var(--space-6) var(--space-6) var(--space-6);
}

.mod-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

.mod-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mod-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* ===== LOADING STATES ===== */
/* Phase 1: Folder Scanning */
.folder-scanning-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
  min-height: 400px;
}

/* Phase 2: Mod Processing */
.mod-processing-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-8);
  gap: var(--space-8);
}

.loading-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: var(--space-3);
  max-width: 600px;
}

.loading-subtext {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0;
  opacity: 0.8;
}

.skeleton-container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  /* Smooth transition when skeleton appears */
  opacity: 0;
  animation: skeleton-container-fade-in 0.8s ease-out 0.5s forwards;
}

@keyframes skeleton-container-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--plumbob-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes thumbnail-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ACCESSIBILITY: REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  .thumbnail-grid,
  .thumbnail-item,
  .skeleton-container {
    transition: none;
    animation: none;
    opacity: 1;
    transform: none;
  }

  .thumbnail-item--loaded {
    animation: none;
  }

  @keyframes thumbnail-fade-in,
  @keyframes skeleton-container-fade-in {
    from, to {
      opacity: 1;
      transform: none;
    }
  }
}

.loading-text {
  font-family: var(--font-family-system);
  font-size: var(--text-lg);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--space-4);
}

.empty-state__title {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-3) 0;
}

.empty-state__description {
  font-family: var(--font-family-system);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0 0 var(--space-6) 0;
  max-width: 480px;
}

.empty-state__action {
  padding: var(--space-3) var(--space-6);
  background: var(--primary-coral);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
  box-shadow:
    0 2px 4px rgba(238, 108, 77, 0.3),
    var(--shadow-accent);
}

.empty-state__action:hover {
  background: var(--primary-orange);
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(238, 108, 77, 0.4),
    var(--shadow-accent);
}

.empty-state__action:hover {
  background: var(--plumbob-green-dark);
  box-shadow: 0 4px 8px rgba(45, 159, 43, 0.3);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE HEADER DESIGN ===== */

@media (max-width: 1024px) {
  .header-top {
    padding: var(--space-4) var(--space-6) var(--space-3) var(--space-6);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
  }

  .header-middle {
    padding: 0 var(--space-6) var(--space-3) var(--space-6);
  }

  .header-bottom {
    padding: var(--space-3) var(--space-6) var(--space-4) var(--space-6);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
    margin-bottom: 0;
  }

  .filters-container {
    width: 100%;
    justify-content: flex-start;
  }

  .header-controls {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .header-top {
    padding: var(--space-3) var(--space-4) var(--space-2) var(--space-4);
  }

  .header-title {
    font-size: var(--text-2xl);
  }

  .header-subtitle {
    font-size: var(--text-xs);
  }

  .header-actions {
    gap: var(--space-2);
  }

  .action-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .action-btn-icon {
    width: 14px;
    height: 14px;
  }

  .header-middle {
    padding: 0 var(--space-4) var(--space-2) var(--space-4);
  }

  .search-input {
    height: 48px;
    font-size: var(--text-base);
  }

  .header-bottom {
    padding: 0 var(--space-4) var(--space-3) var(--space-4);
  }

  .filters-container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
    width: 100%;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .filter-select {
    min-width: 120px;
    flex: 1;
    max-width: 200px;
  }

  .header-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .results-info {
    align-self: flex-start;
  }

  .view-controls {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .header-top {
    padding: var(--space-2) var(--space-3) var(--space-2) var(--space-3);
  }

  .header-title {
    font-size: var(--text-xl);
  }

  .header-subtitle {
    display: none; /* Hide subtitle on very small screens */
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
    gap: var(--space-2);
  }

  .action-btn {
    width: 100%;
    justify-content: center;
    padding: var(--space-2);
  }

  .version-badge {
    align-self: flex-end;
  }

  .header-middle {
    padding: 0 var(--space-3) var(--space-2) var(--space-3);
  }

  .search-input {
    height: 44px;
    padding: 0 var(--space-10) 0 var(--space-10);
    font-size: var(--text-sm);
  }

  .search-icon {
    left: var(--space-3);
    width: 18px;
    height: 18px;
  }

  .header-bottom {
    padding: 0 var(--space-3) var(--space-2) var(--space-3);
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .filter-select {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }

  .thumbnail-size-controls {
    width: 100%;
    justify-content: center;
  }

  .size-btn {
    flex: 1;
    max-width: 60px;
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-tight);
}

/* ===== THUMBNAIL GALLERY - APPLE-INSPIRED DESIGN ===== */

.thumbnail-gallery {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px 24px 24px; /* 24px padding as per handoff requirements */
}

.thumbnail-grid {
  display: grid;
  gap: 16px; /* 16px gaps as per handoff requirements */
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  align-items: start;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.thumbnail-grid--loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive thumbnail sizes */
.thumbnail-grid--small {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.thumbnail-grid--medium {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.thumbnail-grid--large {
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
}

.thumbnail-item {
  background: var(--bg-primary);
  border-radius: 8px; /* 8px border-radius for modern look as per handoff */
  overflow: hidden;
  transition: all 0.2s ease-out;
  cursor: pointer;
  border: 1px solid var(--border-light);
  position: relative;
  /* Subtle sophisticated shadow system */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    var(--shadow-subtle);
  /* Initial state for loading animation */
  opacity: 0;
  transform: translateY(20px);
}

.thumbnail-item--loaded {
  animation: thumbnail-fade-in 0.6s ease-out forwards;
}

/* WCAG 2.1 AA Focus Indicators for Keyboard Navigation */
.thumbnail-item:focus {
  outline: none;
  border-color: var(--plumbob-blue-500);
  box-shadow:
    0 0 0 3px var(--plumbob-blue-200),
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    var(--shadow-subtle);
}

.thumbnail-item:focus-visible {
  outline: 3px solid var(--plumbob-blue-500);
  outline-offset: 2px;
}

/* Elevation hierarchy - Sophisticated accent system */
.thumbnail-item:hover {
  /* Enhanced shadow with subtle coral accent */
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06),
    var(--shadow-hover),
    0 0 0 1px rgba(238, 108, 77, 0.1);
  transform: translateY(-2px);
  border-color: var(--border-accent);
}

.thumbnail-item:active {
  /* Pressed state - reduced shadow for tactile feedback */
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 1px 1px rgba(0, 0, 0, 0.06);
  transform: translateY(0px);
}

.thumbnail-item:focus {
  outline: none;
  border-color: var(--plumbob-blue-500);
  /* Maintain elevated shadow on focus */
  box-shadow:
    0 0 0 3px var(--plumbob-blue-200),
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.thumbnail-image {
  position: relative;
  width: 100%;
  height: 200px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease-out;
}

.thumbnail-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.fallback-icon {
  width: 48px;
  height: 48px;
  color: var(--text-tertiary);
}

.thumbnail-overlay {
  padding: 16px; /* Consistent padding */
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  /* Subtle text shadow for better readability */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.thumbnail-title {
  font-family: var(--font-family-display);
  font-size: var(--text-lg); /* Increased from --text-base for better hierarchy */
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-2) 0; /* Increased spacing for better separation */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  hyphens: auto;
}

.thumbnail-author {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium); /* Increased from normal for better readability */
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.8; /* Subtle opacity for better hierarchy */
}

/* Quality and File Type Badges */
.quality-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: var(--font-family-system);
  font-size: var(--text-xs);
  font-weight: var(--font-bold); /* Increased for better readability */
  line-height: var(--leading-tight); /* Improved from leading-none for better readability */
  letter-spacing: var(--tracking-wide);
  background: var(--primary-purple);
  color: white;
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  /* Sophisticated purple glow for premium feel */
  box-shadow:
    0 1px 3px rgba(102, 44, 145, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 6px rgba(102, 44, 145, 0.2);
}

.file-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: var(--font-family-system);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold); /* Increased for better readability */
  line-height: var(--leading-tight); /* Improved from leading-none for better readability */
  letter-spacing: var(--tracking-wide);
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  /* Subtle shadow for badge depth */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.file-type-script {
  background: var(--primary-coral);
  color: white;
  /* Coral accent for script files */
  box-shadow:
    0 1px 3px rgba(238, 108, 77, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.file-type-package {
  background: var(--primary-teal);
  color: white;
  /* Teal accent for package files */
  box-shadow:
    0 1px 3px rgba(23, 163, 152, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== RESPONSIVE THUMBNAIL DESIGN ===== */

@media (max-width: 1024px) {
  .thumbnail-gallery {
    padding: 0 16px 16px 16px;
  }

  .thumbnail-grid--small {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
  }

  .thumbnail-grid--medium {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 14px;
  }

  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .thumbnail-image {
    height: 180px;
  }

  .thumbnail-overlay {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .thumbnail-gallery {
    padding: 0 12px 12px 12px;
  }

  .thumbnail-grid--small {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 8px;
  }

  .thumbnail-grid--medium {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 14px;
  }

  .thumbnail-image {
    height: 160px;
  }

  .thumbnail-overlay {
    padding: 10px;
  }

  .thumbnail-title {
    font-size: var(--text-base); /* Increased from text-sm for better mobile readability */
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
    margin: 0 0 var(--space-1) 0; /* Reduced spacing on mobile */
  }

  .thumbnail-author {
    font-size: var(--text-sm); /* Increased from text-xs for better mobile readability */
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
    font-weight: var(--font-medium); /* Maintain medium weight on mobile */
  }
}

@media (max-width: 480px) {
  .thumbnail-gallery {
    padding: 0 8px 8px 8px;
  }

  .thumbnail-grid--small,
  .thumbnail-grid--medium,
  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .thumbnail-image {
    height: 140px;
  }

  .thumbnail-overlay {
    padding: 8px;
  }

  .fallback-icon {
    width: 32px;
    height: 32px;
  }

  .quality-badge,
  .file-type-badge {
    padding: 2px 6px;
    font-size: 10px;
    line-height: var(--leading-none);
    letter-spacing: var(--tracking-wide);
  }
}

/* ===== MOD DETAILS MODAL TYPOGRAPHY ===== */

.mod-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
  backdrop-filter: blur(4px);
}

.mod-details-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.mod-details-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-details-title-section {
  flex: 1;
  margin-right: var(--space-4);
}

.mod-details-title {
  font-family: var(--font-family-display);
  font-size: var(--text-3xl); /* Increased for better hierarchy in modal */
  font-weight: var(--font-bold); /* Increased for better emphasis */
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-2) 0; /* Increased spacing */
  word-break: break-word;
}

.mod-details-author {
  font-family: var(--font-family-system);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin: 0;
}

.mod-details-close {
  padding: var(--space-2);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--duration-150) var(--ease-out);
}

.mod-details-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.mod-details-body {
  padding: var(--space-6);
}

.mod-info-grid {
  display: grid;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.mod-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.mod-info-label {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.mod-info-value {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  text-align: right;
}

.mod-additional-info h3 {
  font-family: var(--font-family-display);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-3) 0;
}

.mod-additional-info p {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0 0 var(--space-2) 0;
}

@media (max-width: 768px) {
  .mod-details-modal {
    padding: var(--space-2);
  }

  .mod-details-title {
    font-size: var(--text-2xl); /* Increased for better mobile hierarchy */
    line-height: var(--leading-snug); /* Better mobile line height */
  }

  .mod-details-author {
    font-size: var(--text-sm);
  }

  .mod-details-header,
  .mod-details-body {
    padding: var(--space-4);
  }

  .mod-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .mod-info-value {
    text-align: left;
  }
}
</style>
