#!/usr/bin/env tsx

/**
 * Test script to verify RLE2 thumbnail extraction using S4TK DdsImage
 */

import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import { DdsImage } from '@s4tk/images';

const TIFFANY_PACKAGE_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods\\KiaraZurk_TiffanyHairstyle\\KiaraZurk_TiffanyHairstyle.package';

/**
 * Extracts Buffer from S4TK resource (copied from ThumbnailExtractionService)
 */
function extractBufferFromResource(resource: any): Buffer | null {
    try {
        // Check if resource.value is already a Buffer
        if (resource.value instanceof Buffer) {
            return resource.value;
        }

        // Check if it's an S4TK resource with buffer cache
        if (resource.value && typeof resource.value === 'object') {
            const rawResource = resource.value as any;

            // Try to access the buffer from various S4TK resource structures
            if (rawResource._bufferCache?.buffer instanceof Buffer) {
                return rawResource._bufferCache.buffer;
            }

            if (rawResource.buffer instanceof Buffer) {
                return rawResource.buffer;
            }

            // Try to get the buffer using S4TK's buffer property
            if (typeof rawResource.getBuffer === 'function') {
                return rawResource.getBuffer();
            }
        }

        console.warn('Could not extract buffer from resource:', typeof resource.value);
        return null;
    } catch (error) {
        console.warn('Error extracting buffer from resource:', error);
        return null;
    }
}

async function testRle2Extraction() {
    console.log('🧪 Testing RLE2 Thumbnail Extraction...\n');
    
    try {
        if (!fs.existsSync(TIFFANY_PACKAGE_PATH)) {
            console.log(`❌ Package file not found: ${TIFFANY_PACKAGE_PATH}`);
            return;
        }

        // Read and parse the package
        const packageBuffer = fs.readFileSync(TIFFANY_PACKAGE_PATH);
        const s4tkPackage = Package.from(packageBuffer);
        
        console.log(`📦 Package loaded: ${path.basename(TIFFANY_PACKAGE_PATH)}`);
        console.log(`🔢 Total resources: ${s4tkPackage.size}`);

        // Find RLE2 image resources
        const rle2Resources = [];
        for (const entry of s4tkPackage.entries.values()) {
            if (entry.key.type === BinaryResourceType.Rle2Image) {
                rle2Resources.push(entry);
            }
        }

        console.log(`🖼️ Found ${rle2Resources.length} RLE2 image resources\n`);

        if (rle2Resources.length === 0) {
            console.log('❌ No RLE2 resources found to test');
            return;
        }

        // Test the first few RLE2 resources
        const resourcesToTest = rle2Resources.slice(0, 3);
        
        for (let i = 0; i < resourcesToTest.length; i++) {
            const entry = resourcesToTest[i];
            console.log(`\n--- Testing RLE2 Resource ${i + 1} ---`);
            console.log(`Key: ${entry.key.type.toString(16)}-${entry.key.group.toString(16)}-${entry.key.instance.toString(16)}`);

            // Extract buffer properly
            const buffer = extractBufferFromResource(entry);
            if (!buffer) {
                console.log(`❌ Failed to extract buffer from resource ${i + 1}`);
                continue;
            }

            console.log(`Size: ${buffer.length} bytes`);

            try {
                // RLE2 appears to be zlib-compressed DDS, try to decompress first
                console.log(`   First 4 bytes: ${Array.from(buffer.slice(0, 4)).map(b => '0x' + b.toString(16)).join(', ')}`);

                let decompressedBuffer: Buffer;
                try {
                    // Try zlib decompression
                    decompressedBuffer = zlib.inflateSync(buffer);
                    console.log(`✅ Successfully decompressed RLE2 data`);
                    console.log(`   Original size: ${buffer.length} bytes`);
                    console.log(`   Decompressed size: ${decompressedBuffer.length} bytes`);
                } catch (zlibError) {
                    console.log(`❌ Failed to decompress with zlib: ${zlibError.message}`);
                    // Try direct DDS parsing as fallback
                    decompressedBuffer = buffer;
                }

                // Try to decode with S4TK DdsImage
                const ddsImage = DdsImage.from(decompressedBuffer);
                
                console.log(`✅ Successfully parsed with S4TK DdsImage`);
                console.log(`   Shuffled: ${ddsImage.isShuffled}`);
                
                // Convert to Jimp
                const jimpImage = ddsImage.toJimp();
                console.log(`✅ Successfully converted to Jimp`);
                console.log(`   Dimensions: ${jimpImage.getWidth()}x${jimpImage.getHeight()}`);
                
                // Convert to PNG buffer
                const pngBuffer = await jimpImage.getBufferAsync('image/png');
                console.log(`✅ Successfully converted to PNG`);
                console.log(`   PNG size: ${pngBuffer.length} bytes`);
                
                // Save a test image to verify it works
                const outputPath = `test-rle2-${i + 1}.png`;
                fs.writeFileSync(outputPath, pngBuffer);
                console.log(`💾 Saved test image: ${outputPath}`);
                
            } catch (error) {
                console.log(`❌ Failed to decode RLE2 resource ${i + 1}:`, error.message);
            }
        }

        console.log('\n🎉 RLE2 extraction test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testRle2Extraction().catch(console.error);
